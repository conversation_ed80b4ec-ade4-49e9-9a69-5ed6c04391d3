# build program
ARG IMAGE_PREFIX
FROM ${IMAGE_PREFIX}golang:1.19-alpine AS builder

ARG VERSION
ARG GOPROXY
ARG GOPRIVATE
ENV LDFLAGS="-s"

COPY . /src
WORKDIR /src

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk -U --no-cache add build-base git gcc bash make
#RUN make build
ENV CGO_ENABLED 1
RUN go build -tags sqlite

# create image
FROM ${IMAGE_PREFIX}alpine:3.16

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk --no-cache add curl
RUN addgroup -S ory; \
    adduser -S ory -G ory -D  -h /home/<USER>/bin/nologin; \
    chown -R ory:ory /home/<USER>

RUN apk --no-cache --latest upgrade
RUN apk --no-cache --upgrade --latest add ca-certificates

COPY --from=builder /src/keto /usr/bin/keto
#COPY --from=builder /src/config/keto-local.yml /home/<USER>/keto.yml
COPY --from=builder --chown=ory:ory /src/namespaces.keto.ts /home/<USER>/namespaces.keto.ts
COPY --from=builder --chown=ory:ory /src/tuples.txt /home/<USER>/tuples.txt
COPY --from=builder --chown=ory:ory /src/ketow.sh /home/<USER>/
COPY --from=builder --chown=ory:ory /src/init-tuples.sh /home/<USER>/
RUN chmod +x /home/<USER>/ketow.sh /home/<USER>/init-tuples.sh

# Exposing the ory home directory to simplify passing in keto configuration (e.g. if the file $HOME/keto.yaml
# exists, it will be automatically used as the configuration file).
VOLUME /home/<USER>

# Declare the standard ports used by keto (4433 for read service endpoint, 4434 for write service endpoint)
EXPOSE 4433 4434 4466 4467

USER ory

ENTRYPOINT ["keto"]
CMD ["serve", "--sqa-opt-out"]
