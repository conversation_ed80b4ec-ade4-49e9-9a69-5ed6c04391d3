server:
  http:
    addr: 0.0.0.0:8010
    timeout: 100s
  grpc:
    addr: 0.0.0.0:8011
    timeout: 100s
  # non-empty comma-separated list of origins will enable CORS
  #cors_origins: "*"
data:
  database:
    driver: postgres
    #source: postgres://root:root@localhost:5432/anno?sslmode=disable
    endpoint: postgres
    port: 5432
    database: anno_v2
    username: root
    password: root
    options: sslmode=disable
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

temporal:
  disable_worker: false
  addr: temporal:7233
  namespace:
  task_queue: anno

rpc:
  # service account used for RPC calls
  svc_account: aaaaaaaanno

  iam:
    addr: iam:8001
    timeout: 30s
  annofeed:
    addr: annofeed:8021
    timeout: 30s
  annout:
    addr: annout:8031

otel:
  tracing:
    endpoint: #127.0.0.1:4317
  metrics:
    serve_addr: #:6060
  log:
    level: debug # info
    format: default

mq:
  producer:
#    provider: aws_sns
    provider: redis_pubsub
#    topic: arn:aws-cn:sns:cn-northwest-1:************:non-prod-workload-sns-sansheng-anno
    topic: test-anno-mq
  consumer:
    provider: #aws_sqs
    handler_threads: 4
    # comma seperated topics list (not for AWS SQS consumers)
    topics: ""
  sqs:
    name: non-prod-workload-sqs-sansheng-anno


object_storage:
  # storage type:
  # "s3": store the uploaded files into S3;
  # "localfs": use local filesystem; this is the default;
  type: s3

  # directory in local filesystem to put temporary files: e.g. downloaded files
  workdir:

  # local storage config
  localfs:
    # directory in local filesystem to put the uploaded files
    base_dir: upload

  s3:
    access_key: ********************
    secret_key: Q95HadgHsDPsXaRc4z3n0H2NfrS9GGFO5yUQfCMq

    # bucket for normal data
    bucket: non-prod-workload-sansheng
    # bucket for public resources: user/project/label avatars
    public_bucket: konvery-images-public-nonprod

    # default expires duration for signed URLs: 7 days (max is 7 days)
    expires: 604800s

  cloudfront:
    enabled: false
    # signer key ID
    sign_key_id: KJBD2UMX2E3GW
    # signer private key (RSA) in PEM format
    sign_key: |
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    # default expires duration for signed URLs: 7 days (max is 7 days)
    expires: 604800s

    distributions:
      dist1:
        # matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
        origin: s3://konvery-images-public-nonprod
        # access URL prefix, including scheme and domain name: https://example.com
        url_prefix: https://s3npip.d.konvery.com
        # if it is a public distribution
        public: true
      dist2:
        origin: s3://non-prod-workload-sansheng
        url_prefix: https://s3npss.d.konvery.com
