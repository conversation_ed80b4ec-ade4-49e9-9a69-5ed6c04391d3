server {
    listen       443 ssl;
    server_name  local.konvery.work;
    charset utf-8;

    ssl_certificate /etc/nginx/ssl/local.konvery.work.pem;
    ssl_certificate_key /etc/nginx/ssl/local.konvery.work-key.pem;
 
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout  10m;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        #return 302 $scheme://$host$request_uri;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}

    location /iam/ {
        proxy_pass http://iam:8000/;

        add_header Access-Control-Allow-Methods 'GET,HEAD,POST,PUT,DELETE,OPTIONS,PATCH' always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Origin $http_origin always;
        #add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Headers $http_access_control_request_headers always;
        if ($request_method = OPTIONS) {
            return 200;
        }
    }

    location /anno/ {
        proxy_pass http://anno:8010/;

        add_header Access-Control-Allow-Methods 'GET,HEAD,POST,PUT,DELETE,OPTIONS,PATCH' always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Origin $http_origin always;
        #add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Headers $http_access_control_request_headers always;
        if ($request_method = OPTIONS) {
            return 200;
        }
    }

    location /annofeed/ {
        proxy_pass http://annofeed:8020/;

        add_header Access-Control-Allow-Methods 'GET,HEAD,POST,PUT,DELETE,OPTIONS,PATCH' always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Origin $http_origin always;
        #add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Headers $http_access_control_request_headers always;
        if ($request_method = OPTIONS) {
            return 200;
        }
    }

    location /annout/ {
        proxy_pass http://annout:8030/;

        add_header Access-Control-Allow-Methods 'GET,HEAD,POST,PUT,DELETE,OPTIONS,PATCH' always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Origin $http_origin always;
        #add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Headers $http_access_control_request_headers always;
        if ($request_method = OPTIONS) {
            return 200;
        }
    }

    location /annostat/ {
        proxy_pass http://annostat:8040/;

        add_header Access-Control-Allow-Methods 'GET,HEAD,POST,PUT,DELETE,OPTIONS,PATCH' always;
        add_header Access-Control-Max-Age 3600 always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Allow-Origin $http_origin always;
        #add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Headers $http_access_control_request_headers always;
        if ($request_method = OPTIONS) {
            return 200;
        }
    }
}
