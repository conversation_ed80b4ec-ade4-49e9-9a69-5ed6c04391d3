#!/bin/bash

# Build script for separate build and run solution
# Usage: ./build.sh [service_name]
# If no service_name provided, builds all services

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径配置
PROJECT_BASE_PATH="/Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev"
DOCKER_CONFIG_PATH="/Users/<USER>/docker/local-dev/build-separate-solution"

# 服务配置 - 兼容低版本 bash，使用函数替代关联数组
get_project_name() {
    case "$1" in
        "keto") echo "keto" ;;
        "iam") echo "iam" ;;
        "anno") echo "anno" ;;
        "annofeed") echo "annofeed" ;;
        "annout") echo "annout" ;;
        "annostat") echo "annostat" ;;
        "anycorn") echo "unicorn" ;;
        "tars") echo "unicorn" ;;
        *) echo "" ;;
    esac
}

# 获取所有服务列表
get_all_services() {
    echo "keto iam anno annofeed annout annostat anycorn tars"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目路径是否存在
check_project_path() {
    local service_name=$1
    local project_name=$(get_project_name "$service_name")
    local project_path="$PROJECT_BASE_PATH/$project_name"

    if [ ! -d "$project_path" ]; then
        log_error "Project path not found: $project_path"
        return 1
    fi

    log_info "Project path found: $project_path"
    return 0
}

# 创建临时构建目录
create_temp_build_dir() {
    local service_name=$1
    local temp_dir="./tmp/docker-build-$service_name-$$"

    log_info "Creating temporary build directory: $temp_dir" >&2
    mkdir -p "$temp_dir"
    printf "%s" "$temp_dir"
}

# 复制项目源码到临时目录
copy_source_code() {
    local service_name=$1
    local project_name=$(get_project_name "$service_name")
    local source_path="$PROJECT_BASE_PATH/$project_name"
    local temp_dir=$2

    log_info "Copying source code from $source_path to $temp_dir"
    cp -r "$source_path"/* "$temp_dir/"

    # 确保有 go.mod 文件
    if [ ! -f "$temp_dir/go.mod" ]; then
        log_error "go.mod not found in $temp_dir"
        return 1
    fi

    return 0
}

# 构建单个服务
build_service() {
    local service_name=$1
    
    log_info "Starting build for service: $service_name"
    
    # 检查服务是否存在
    local project_name=$(get_project_name "$service_name")
    if [ -z "$project_name" ]; then
        log_error "Unknown service: $service_name"
        log_info "Available services: $(get_all_services)"
        return 1
    fi
    
    # 检查项目路径
    if ! check_project_path "$service_name"; then
        return 1
    fi
    
    # 检查 Dockerfile 是否存在
    local dockerfile_path="$DOCKER_CONFIG_PATH/projects/$service_name/Dockerfile"
    if [ ! -f "$dockerfile_path" ]; then
        log_error "Dockerfile not found: $dockerfile_path"
        return 1
    fi
    
    # 创建临时构建目录
    local temp_dir
    temp_dir=$(create_temp_build_dir "$service_name")

    # 设置清理函数
    cleanup() {
        log_info "Cleaning up temporary directory: $temp_dir"
        rm -rf "$temp_dir"
    }
    trap cleanup EXIT

    # 复制源码
    if ! copy_source_code "$service_name" "$temp_dir"; then
        return 1
    fi
    
    # 构建 Docker 镜像
    log_info "Building Docker image for $service_name"
    local image_name="local-dev-$service_name:latest"
    
    if docker build \
        -t "$image_name" \
        -f "$dockerfile_path" \
        "$temp_dir"; then
        log_success "Successfully built image: $image_name"
    else
        log_error "Failed to build image for $service_name"
        return 1
    fi
    
    return 0
}

# 构建所有服务
build_all_services() {
    log_info "Building all services..."

    local failed_services=""
    local success_count=0
    local total_count=0
    local all_services=$(get_all_services)

    # 计算总数
    for service in $all_services; do
        total_count=$((total_count + 1))
    done

    local current_count=0
    for service_name in $all_services; do
        current_count=$((current_count + 1))
        log_info "Building service $current_count/$total_count: $service_name"

        if build_service "$service_name"; then
            success_count=$((success_count + 1))
        else
            if [ -z "$failed_services" ]; then
                failed_services="$service_name"
            else
                failed_services="$failed_services $service_name"
            fi
        fi

        echo "----------------------------------------"
    done

    # 输出构建结果
    echo
    log_info "Build Summary:"
    log_success "Successfully built: $success_count/$total_count services"

    if [ -n "$failed_services" ]; then
        log_error "Failed services: $failed_services"
        return 1
    else
        log_success "All services built successfully!"
        return 0
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [service_name]"
    echo
    echo "Build Docker images for services."
    echo
    echo "Options:"
    echo "  service_name    Build specific service (optional)"
    echo "  -h, --help      Show this help message"
    echo
    echo "Available services:"
    for service in $(get_all_services); do
        local project_name=$(get_project_name "$service")
        echo "  - $service ($project_name)"
    done
    echo
    echo "Examples:"
    echo "  $0              # Build all services"
    echo "  $0 keto         # Build only keto service"
    echo "  $0 iam          # Build only iam service"
}

# 主函数
main() {
    # 检查参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    # 检查 Docker 是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # 根据参数决定构建策略
    if [ $# -eq 0 ]; then
        # 构建所有服务
        build_all_services
    elif [ $# -eq 1 ]; then
        # 构建指定服务
        build_service "$1"
    else
        log_error "Too many arguments"
        show_help
        exit 1
    fi
}

# 执行主函数
main "$@"
