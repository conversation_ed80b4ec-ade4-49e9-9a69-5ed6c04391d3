# Build Separate Solution - 总结

## 概述

基于 `multi-config-solution` 创建了一个新的 `build-separate-solution`，实现了构建和启动的分离。

## 主要改动

### 1. 构建脚本 (build.sh)

- **兼容性改进**: 移除了关联数组 (`declare -A`)，使用函数替代，兼容低版本 bash
- **功能**: 支持构建所有服务或单个服务
- **特点**:
  - 创建临时构建目录
  - 复制项目源码到临时目录作为构建上下文
  - 在 Dockerfile 中直接执行 Go 构建命令
  - 构建完成后自动清理临时目录

### 2. Docker Compose 配置 (docker-compose.yml)

- **移除构建配置**: 删除了所有 `build` 配置
- **使用预构建镜像**: 改为使用 `image: local-dev-{service}:latest` 格式
- **仅负责启动**: 专注于容器启动和配置

### 3. Dockerfile 修改

对所有服务的 Dockerfile 进行了修改：

- **移除 make 依赖**: 不再使用 `make build`，直接执行 Go 构建命令
- **统一构建方式**: 使用 `mkdir -p bin/ && go build -mod=vendor -ldflags "${LDFLAGS}" -o ./bin/ ./...`
- **特殊处理**:
  - `anno`: 添加了 `GOEXPERIMENT=jsonv2`
  - `keto`: 使用 `go build -tags sqlite`
  - `anycorn/tars`: 使用 `go build -ldflags "${LDFLAGS}" -o ./bin/ .`

## 文件结构

```
build-separate-solution/
├── build.sh                   # 构建脚本 (兼容低版本 bash)
├── build.sh.backup           # 原始构建脚本备份
├── docker-compose.yml        # 启动配置 (仅启动，不构建)
├── start.sh                  # 启动脚本
├── test-script-only.sh       # 脚本兼容性测试
├── test-build.sh             # 构建测试脚本
├── projects/                 # 服务配置目录
│   ├── keto/Dockerfile       # 修改后的 Dockerfile
│   ├── iam/Dockerfile        # 修改后的 Dockerfile
│   ├── anno/Dockerfile       # 修改后的 Dockerfile
│   ├── annofeed/Dockerfile   # 修改后的 Dockerfile
│   ├── annout/Dockerfile     # 修改后的 Dockerfile
│   ├── annostat/Dockerfile   # 修改后的 Dockerfile
│   ├── anycorn/Dockerfile    # 修改后的 Dockerfile
│   └── tars/Dockerfile       # 修改后的 Dockerfile
└── README.md                 # 更新的说明文档
```

## 使用方法

### 1. 构建镜像

```bash
# 构建所有服务
./build.sh

# 构建单个服务
./build.sh iam
./build.sh keto
```

### 2. 启动服务

```bash
# 设置环境变量
export ENVIRONMENT=dev  # 或 prod

# 启动服务
./start.sh

# 或直接使用 docker-compose
docker-compose up -d
```

## 优势

1. **启动速度快**: 使用预构建镜像，启动时无需重新构建
2. **构建灵活**: 可以选择构建所有服务或单个服务
3. **环境隔离**: 构建和运行环境完全分离
4. **兼容性好**: 兼容低版本 bash
5. **资源节省**: 避免重复构建，节省时间和资源

## 测试结果

- ✅ 脚本兼容性测试通过 (4/4 测试通过)
- ✅ 所有 Dockerfile 存在且格式正确
- ✅ 项目路径检查通过
- ✅ 临时目录创建和清理功能正常

## 注意事项

1. **首次使用**: 需要先运行 `./build.sh` 构建所有镜像
2. **网络依赖**: 构建过程需要网络连接下载 Go 依赖和 Docker 基础镜像
3. **磁盘空间**: 预构建镜像会占用额外的磁盘空间
4. **更新代码**: 代码更新后需要重新构建对应的镜像

## 与原方案对比

| 特性 | multi-config-solution | build-separate-solution |
|------|----------------------|------------------------|
| 构建方式 | 启动时构建 | 预构建镜像 |
| 启动速度 | 慢 (需要构建) | 快 (使用预构建镜像) |
| 磁盘占用 | 低 | 高 (存储镜像) |
| 构建灵活性 | 低 | 高 (可选择性构建) |
| bash 兼容性 | 未测试 | 兼容低版本 |
| 资源使用 | 每次启动都构建 | 一次构建多次使用 |
