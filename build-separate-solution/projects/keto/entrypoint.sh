#!/bin/sh
# 设置默认环境为 dev
ENVIRONMENT=${ENVIRONMENT:-dev}

# 将对应环境的配置文件复制为程序默认加载的文件
cp /home/<USER>/config/keto-${ENVIRONMENT}.yml /home/<USER>/keto.yml

# 打印信息方便调试
echo "Using configuration: /home/<USER>/keto-${ENVIRONMENT}.yml -> /home/<USER>/keto.yml"

cat /home/<USER>/keto.yml | grep dsn

# 执行原程序
exec /usr/bin/keto serve -c /home/<USER>/keto.yml  --sqa-opt-out
# "$@" 会将所有传递给脚本的参数原封不动地传递给下一个命令
# exec "$@" 