server:
  http:
    addr: 0.0.0.0:8050
    timeout: 100s
    # non-empty comma-separated list of origins will enable CORS
    #cors_origins: "*"
  grpc:
    addr: 0.0.0.0:8051
    timeout: 100s
data:
  database:
    driver: postgres
    #source: postgres://root:root@localhost:5432/anyconn?sslmode=disable
    endpoint: postgres
    port: 5432
    database: anyconn
    username: root
    password: root
    options: sslmode=disable
    max_idle_conns: 5
    max_open_conns: 20
    conn_max_idle_time: 600s
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

temporal:
  disable_worker: false
  addr: temporal:7233
  namespace:
  task_queue: anyconn

ktwf:
  master:
    #image: artifactory.rp.konvery.work/docker/sansheng-unicorn:latest
    #command: ["/app/unicorn", "--conf", "/data/conf/anyconn.conf", "anyconn", "serve"]
    args: []
    #service_account: sansheng-anyconn
    storage_class: ebs-kvy
  worker:
    namespace: sansheng-sandbox
    service_account: default

rpc:
  # service account used for RPC calls
  svc_account: aaaaaaaanno

  services:
    iam:
      addr: iam:8001
    anno:
      addr: anno:8011

otel:
  tracing:
    endpoint: #127.0.0.1:4317
  metrics:
    serve_addr: #:6060
  log:
    level: info
    format: default

# CAUTION: Never configure these in production!
danger_test:

custsys:
  customers:
    dazhuo:
      services:
        filemanager:
          # token service endpoint
          endpoint: https://zdrive-bigdata.mychery.com/operation/operationManager/token/auth
          access_key: KonveryAnnotation # username
          secret_key: K0xKlB06 # password
          concurrency: 10 # concurrent downloading threads
          extra:
            # check service endpoint
            check_url: https://zdrive-bigdata.mychery.com/manager/dataManager/task/send/label/labelingValidation
            # fetch model check results
            model_result_url: https://zdrive-bigdata.mychery.com/manager/dataManager/task/model/external/result
    testin:
      services:
        default:
          access_key:
          # extra:
          #   comment_url: https://label-std.testin.cn/v2/issues # 批注
    baidu:
      services:
        default:
          access_key: ak
          secret_key: sk
          concurrency: 1 # concurrent downloading threads
          extra:
            host: https://data-ai.baidu.com
            download_data_path: /api/v1/external/send_labeled_data/download
            get_secret_path: /api/v1/external/get_key_plaintext
            notify_labeled_data_path: /api/v1/external/notice_labeled_data

lark:
  app_id:
  app_sec:
  channels:
    dazhuo:
      receive_id: 
      template_id: ctp_AAqkD1J3FVoS
    whookevt:
      receive_id: 
      template_id: ctp_AAqkD1J3FVoS
    baidu:
      receive_id:
      template_id: ctp_AAqkD1J3FVoS

mq:
  producer:
    provider: #aws_sns
    topic: arn:aws-cn:sns:cn-northwest-1:035532479701:non-prod-workload-sns-sansheng-anyconn
  consumer:
    provider: #aws_sqs
    handler_threads: 4
    # comma seperated topics list (not for AWS SQS consumers)
    topics: ""
  sqs:
    name: non-prod-workload-sqs-sansheng-anyconn

workspace:

file_server:
  storage: s3 #localfs

  # local storage config
  # directory in local filesystem to put the uploaded files
  base_dir: upload

  # s3 storage config
  # store normal data
  bucket: non-prod-workload-sansheng
  # store public access resources: user/project/label avatars
  public_bucket: konvery-images-public-nonprod

  # max is 7 days, set to 3 days
  presign_expires: 259200s

  s3:
    access_key:
    secret_key:

  cloudfront:
    enabled: false
    # signer key ID
    sign_key_id:
    # signer private key (RSA) in PEM format
    sign_key:
    # default expires duration for signed URLs: 3 days
    expires: 259200s

    distributions:
      dist1:
        # matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
        origin: s3://konvery-images-public-nonprod
        # access URL prefix, including scheme and domain name: https://example.com
        url_prefix: https://s3npip.d.konvery.com
        # if it is a public distribution
        public: true
      dist2:
        origin: s3://non-prod-workload-sansheng
        url_prefix: https://s3npss.d.konvery.com
