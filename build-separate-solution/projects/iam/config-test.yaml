server:
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:8001
    timeout: 1s
  enable_auth: true
  # non-empty comma-separated list of origins will enable CORS
  #cors_origins: "*"
  cookie_domain: "local.konvery.work"
data:
  database:
    driver: postgres
    #source: postgres://root:root@localhost:5432/iam?sslmode=disable
    endpoint: postgres
    port: 5432
    database: iam_v2
    username: root
    password: root
    options: sslmode=disable
    max_idle_conns: 5
    max_open_conns: 20
    conn_max_idle_time: 600s
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
notice:
  addr: 127.0.0.1:9100
jwt:
  sign_method: HS256
  sign_key: "abc" # specify a key and keep it a secret
  issuer: Konvery-IAM
  audience:
  ttl: 86400s # 24 hours
alisms:
  access_key:
  secret_key:
  region_id: cn-hangzhou
  sign_name: 恺望数据
  template_id: SMS_246725303

temporal:
  disable_worker: false
  addr: temporal:7233
  namespace:
  task_queue: iam

keto:
  read_addr: keto:4466
  write_addr: keto:4467

otel:
  tracing:
    endpoint: #127.0.0.1:4317
  metrics:
    serve_addr: #:6060
  log:
    level: debug # info
    format: default

# CAUTION: Never configure these in production!
danger_test:
#  fake_logins: {}
  fake_logins:
    groupX:
      phone_regexp: "^\\+0[0-9]{4}"
      auth_code: "123456"