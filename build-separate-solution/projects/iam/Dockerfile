# build program
ARG IMAGE_PREFIX
FROM ${IMAGE_PREFIX}golang:1.22-alpine AS builder

ARG VERSION
ARG GOPROXY
ARG GOPRIVATE
ENV LDFLAGS="-s"

COPY . /src
WORKDIR /src

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add git
# Direct Go build instead of make build
RUN mkdir -p bin/ && go build -mod=vendor -ldflags "${LDFLAGS}" -o ./bin/ ./...

# create image
FROM ${IMAGE_PREFIX}alpine:3.18

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add --no-cache ca-certificates tzdata && update-ca-certificates

COPY --from=builder /src/bin /app

RUN mkdir -p /data/conf
#COPY --from=builder /src/configs/config-local.yaml /data/conf/config.yaml

WORKDIR /app

EXPOSE 6060
EXPOSE 8000
EXPOSE 8001
VOLUME /data/conf

CMD ["./iam", "-conf", "/data/conf"]
