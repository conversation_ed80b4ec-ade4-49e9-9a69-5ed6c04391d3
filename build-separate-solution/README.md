# Build Separate Solution

## 概述

这是一个将构建和启动分离的解决方案，支持不同环境的配置切换。构建过程独立于启动过程，使用预构建的 Docker 镜像，启动更快。

## 特点

- ✅ **构建和启动分离**：构建过程独立于启动过程
- ✅ **预构建镜像**：使用预构建的 Docker 镜像，启动更快
- ✅ **多环境支持**：支持 dev/prod 等不同环境配置
- ✅ **直接构建**：在 Dockerfile 中直接执行 Go 构建命令
- ✅ **临时构建目录**：使用临时目录作为构建上下文，避免污染源码
- ✅ **灵活构建**：支持构建所有服务或单个服务

## 文件结构

```
build-separate-solution/
├── docker-compose.yml         # 启动配置文件（仅启动，不构建）
├── build.sh                   # 构建脚本
├── start.sh                   # 启动脚本
├── setup-configs.sh           # 配置文件创建脚本
├── projects/                  # 服务配置文件目录
│   ├── iam/
│   │   ├── Dockerfile         # 构建用 Dockerfile
│   │   ├── config-dev.yaml    # 开发环境配置
│   │   ├── config-test.yaml   # 测试环境配置
│   │   └── config-prod.yaml   # 生产环境配置
│   ├── anno/
│   │   ├── Dockerfile
│   │   ├── config-dev.yaml
│   │   ├── config-test.yaml
│   │   └── config-prod.yaml
│   └── ...                    # 其他服务
└── README.md                  # 说明文档
```

## 使用方法

### 1. 构建所有服务镜像

```bash
./build.sh
```

### 2. 构建单个服务镜像

```bash
./build.sh keto
./build.sh iam
./build.sh anno
```

### 3. 启动服务

```bash
# 设置环境变量
export ENVIRONMENT=dev  # 或 prod

# 启动所有服务
./start.sh

# 或者使用 docker-compose 直接启动
docker-compose up -d
```

## 配置文件

每个服务都有对应的配置文件：
- `projects/*/config-dev.yaml` - 开发环境配置
- `projects/*/config-prod.yaml` - 生产环境配置

## 环境切换

通过设置 `ENVIRONMENT` 环境变量来切换不同的配置：
- `dev` - 开发环境（默认）
- `prod` - 生产环境

## 构建说明

构建脚本会：
1. 为每个项目创建临时构建目录
2. 复制项目源码到临时目录
3. 在 Dockerfile 中直接执行 Go 构建命令（替代 make build）
4. 构建完成后清理临时目录

## 工作原理

### 构建阶段
1. **临时目录创建**：为每个服务创建临时构建目录
2. **源码复制**：将项目源码复制到临时目录作为构建上下文
3. **直接构建**：在 Dockerfile 中直接执行 Go 构建命令，不依赖 make
4. **镜像生成**：生成带有服务名称标签的 Docker 镜像
5. **清理**：构建完成后清理临时目录

### 启动阶段
1. **环境变量控制**：通过 `ENVIRONMENT` 环境变量指定当前环境
2. **预构建镜像**：使用预构建的镜像，无需重新构建
3. **配置文件映射**：动态选择对应环境的配置文件
4. **快速启动**：跳过构建过程，直接启动容器

## 优势

- **启动速度快**：使用预构建镜像，启动时无需重新构建
- **构建灵活**：可以选择构建所有服务或单个服务
- **环境隔离**：构建和运行环境完全分离
- **资源节省**：避免重复构建，节省时间和资源
