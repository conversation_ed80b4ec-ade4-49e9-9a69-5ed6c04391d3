#!/bin/bash

# Test build script for build-separate-solution
# This script tests building a single service to verify the setup

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试构建单个服务
test_build_service() {
    local service_name=${1:-"iam"}
    
    log_info "Testing build for service: $service_name"
    
    # 检查 Docker 是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # 执行构建
    if ./build.sh "$service_name"; then
        log_success "Build test passed for $service_name"
        
        # 检查镜像是否创建成功
        local image_name="local-dev-$service_name:latest"
        if docker images | grep -q "$image_name"; then
            log_success "Docker image created successfully: $image_name"
            
            # 显示镜像信息
            log_info "Image details:"
            docker images | grep "$image_name"
        else
            log_error "Docker image not found: $image_name"
            return 1
        fi
    else
        log_error "Build test failed for $service_name"
        return 1
    fi
}

# 清理测试镜像
cleanup_test_images() {
    local service_name=${1:-"iam"}
    local image_name="local-dev-$service_name:latest"
    
    log_info "Cleaning up test image: $image_name"
    
    if docker images | grep -q "$image_name"; then
        docker rmi "$image_name" || log_warning "Failed to remove image: $image_name"
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [service_name] [--cleanup]"
    echo
    echo "Test build script for build-separate-solution."
    echo
    echo "Options:"
    echo "  service_name    Service to test (default: iam)"
    echo "  --cleanup       Clean up test images after testing"
    echo "  -h, --help      Show this help message"
    echo
    echo "Available services:"
    echo "  - keto"
    echo "  - iam"
    echo "  - anno"
    echo "  - annofeed"
    echo "  - annout"
    echo "  - annostat"
    echo "  - anycorn"
    echo "  - tars"
    echo
    echo "Examples:"
    echo "  $0              # Test build iam service"
    echo "  $0 keto         # Test build keto service"
    echo "  $0 iam --cleanup # Test build iam and cleanup after"
}

# 主函数
main() {
    local service_name="iam"
    local cleanup=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --cleanup)
                cleanup=true
                shift
                ;;
            *)
                service_name="$1"
                shift
                ;;
        esac
    done
    
    log_info "Starting build test for service: $service_name"
    
    # 执行测试
    if test_build_service "$service_name"; then
        log_success "Build test completed successfully!"
        
        if [ "$cleanup" = true ]; then
            cleanup_test_images "$service_name"
        fi
    else
        log_error "Build test failed!"
        exit 1
    fi
}

# 执行主函数
main "$@"
