#!/bin/bash

# Test script functionality without Docker build
# This tests the bash compatibility and script logic

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径配置
PROJECT_BASE_PATH="/Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev"
DOCKER_CONFIG_PATH="/Users/<USER>/docker/local-dev/build-separate-solution"

# 服务配置 - 兼容低版本 bash，使用函数替代关联数组
get_project_name() {
    case "$1" in
        "keto") echo "keto" ;;
        "iam") echo "iam" ;;
        "anno") echo "anno" ;;
        "annofeed") echo "annofeed" ;;
        "annout") echo "annout" ;;
        "annostat") echo "annostat" ;;
        "anycorn") echo "unicorn" ;;
        "tars") echo "unicorn" ;;
        *) echo "" ;;
    esac
}

# 获取所有服务列表
get_all_services() {
    echo "keto iam anno annofeed annout annostat anycorn tars"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_get_project_name() {
    log_info "Testing get_project_name function..."
    
    local services=$(get_all_services)
    for service in $services; do
        local project_name=$(get_project_name "$service")
        if [ -n "$project_name" ]; then
            log_success "Service '$service' -> Project '$project_name'"
        else
            log_error "Failed to get project name for service '$service'"
            return 1
        fi
    done
    
    # Test invalid service
    local invalid_project=$(get_project_name "invalid_service")
    if [ -z "$invalid_project" ]; then
        log_success "Invalid service correctly returns empty string"
    else
        log_error "Invalid service should return empty string"
        return 1
    fi
    
    return 0
}

test_path_checking() {
    log_info "Testing path checking..."
    
    # Test a few services
    for service in iam keto anno; do
        local project_name=$(get_project_name "$service")
        local project_path="$PROJECT_BASE_PATH/$project_name"
        
        if [ -d "$project_path" ]; then
            log_success "Path exists: $project_path"
        else
            log_warning "Path not found: $project_path"
        fi
    done
    
    return 0
}

test_dockerfile_existence() {
    log_info "Testing Dockerfile existence..."
    
    for service in $(get_all_services); do
        local dockerfile_path="$DOCKER_CONFIG_PATH/projects/$service/Dockerfile"
        
        if [ -f "$dockerfile_path" ]; then
            log_success "Dockerfile exists: $dockerfile_path"
        else
            log_error "Dockerfile not found: $dockerfile_path"
        fi
    done
    
    return 0
}

test_temp_dir_creation() {
    log_info "Testing temporary directory creation..."
    
    local service_name="test"
    local temp_dir="/tmp/docker-build-$service_name-$$"
    
    log_info "Creating temporary directory: $temp_dir" >&2
    mkdir -p "$temp_dir"
    
    if [ -d "$temp_dir" ]; then
        log_success "Temporary directory created successfully"
        rm -rf "$temp_dir"
        log_success "Temporary directory cleaned up"
        return 0
    else
        log_error "Failed to create temporary directory"
        return 1
    fi
}

# 主测试函数
main() {
    log_info "Starting script functionality tests..."
    echo
    
    local failed_tests=0
    local total_tests=4
    
    # 运行测试
    if test_get_project_name; then
        log_success "✓ get_project_name test passed"
    else
        log_error "✗ get_project_name test failed"
        failed_tests=$((failed_tests + 1))
    fi
    echo
    
    if test_path_checking; then
        log_success "✓ path checking test passed"
    else
        log_error "✗ path checking test failed"
        failed_tests=$((failed_tests + 1))
    fi
    echo
    
    if test_dockerfile_existence; then
        log_success "✓ Dockerfile existence test passed"
    else
        log_error "✗ Dockerfile existence test failed"
        failed_tests=$((failed_tests + 1))
    fi
    echo
    
    if test_temp_dir_creation; then
        log_success "✓ temporary directory test passed"
    else
        log_error "✗ temporary directory test failed"
        failed_tests=$((failed_tests + 1))
    fi
    echo
    
    # 输出结果
    local passed_tests=$((total_tests - failed_tests))
    log_info "Test Results:"
    log_success "Passed: $passed_tests/$total_tests"
    
    if [ $failed_tests -gt 0 ]; then
        log_error "Failed: $failed_tests/$total_tests"
        return 1
    else
        log_success "All tests passed! Script is compatible with your bash version."
        return 0
    fi
}

# 执行主函数
main "$@"
