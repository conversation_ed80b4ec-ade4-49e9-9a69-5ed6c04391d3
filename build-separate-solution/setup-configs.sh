#!/bin/bash

# 方案2：多配置文件方案 - 配置文件管理脚本
# 为所有服务创建多环境配置文件

set -e

# 服务列表
SERVICES=("iam" "anno" "annofeed" "annout" "annostat" "anycorn" "tars")

# 环境列表
ENVIRONMENTS=("dev" "test" "prod")

echo "🔧 开始为所有服务创建多环境配置文件..."

for service in "${SERVICES[@]}"; do
    echo "📁 处理服务: $service"

    # 检查原始配置文件是否存在
    if [ ! -f "projects/$service/config.yaml" ]; then
        echo "⚠️  跳过 $service: 配置文件不存在"
        continue
    fi

    # 为每个环境创建配置文件
    for env in "${ENVIRONMENTS[@]}"; do
        config_file="projects/$service/config-$env.yaml"

        if [ ! -f "$config_file" ]; then
            echo "   📄 创建 $config_file"
            cp "projects/$service/config.yaml" "$config_file"

            # 根据环境修改配置
            case $env in
                "prod")
                    echo "   🔧 配置生产环境参数..."
                    # 这里可以添加自动化的配置修改逻辑
                    ;;
                "test")
                    echo "   🔧 配置测试环境参数..."
                    # 这里可以添加自动化的配置修改逻辑
                    ;;
            esac
        else
            echo "   ✅ $config_file 已存在"
        fi
    done
done

echo "✅ 配置文件创建完成！"
echo ""
echo "📝 接下来你需要："
echo "1. 编辑各个环境的配置文件，修改数据库连接信息"
echo "2. 使用 ./start.sh [env] 启动对应环境的服务"
echo ""
echo "📋 配置文件列表："
find projects -name "config-*.yaml" | sort
