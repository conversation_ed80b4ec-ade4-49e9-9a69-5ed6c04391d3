# version: '3'
services:

  keto:
    image: local-dev-keto:latest
    container_name: keto
    ports:
      - 4466:4466
      - 4467:4467
    volumes:
      # - ./projects/keto/keto-${ENVIRONMENT:-dev}.yaml:/home/<USER>/keto.yml
      # - ./projects/keto/keto.yml:/home/<USER>/keto.yml # 线上配置
      - ./projects/keto:/home/<USER>/config
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      - "LOG_LEVEL=debug"
      - "DUMP_CACHE=false"
      - "SHOW_DB_QUERY=true"
      - "SHOW_OBJ_NAME=true"
      - "SQA_OPT_OUT=true"
      - "NAMESPACES_EXPERIMENTAL_STRICT_MODE=true"
      - "DBG_CACHE_REFRESH_SECONDS=600"
    entrypoint: ["/home/<USER>/config/entrypoint.sh"]
    networks:
      - dev-network

    # 添加健康检查
    # 线上数据库 数据量大 初始化特别慢 需要延长时间
    healthcheck:
      test: ["CMD-SHELL", "curl -f -6 http://localhost:4466/health/ready || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 50
      start_period: 30s # 容器启动后，等待 30 秒再开始检查


  iam:
    image: local-dev-iam:latest
    container_name: iam
    ports:
      - 8000:8000
      - 8001:8001
    volumes:
      - ./projects/iam/config-${ENVIRONMENT:-dev}.yaml:/data/conf/config.yaml
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
    networks:
      - dev-network
    depends_on:
      keto:
        condition: service_healthy

  anno:
    image: local-dev-anno:latest
    container_name: anno
    ports:
      - 8010:8010
      - 8011:8011
    volumes:
      - ./projects/anno/config-${ENVIRONMENT:-dev}.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      - "LOT_ENABLE_JOB_CAM_PARAMS=true"
    depends_on:
      - iam
      - annofeed
      - annout
      - annostat

  annofeed:
    image: local-dev-annofeed:latest
    container_name: annofeed
    ports:
      - 8020:8020
      - 8021:8021
    networks:
      - dev-network
    volumes:
      - ./projects/annofeed/config-${ENVIRONMENT:-dev}.yaml:/data/conf/config.yaml
      - ./projects/annofeed/token:/var/run/secrets/eks.amazonaws.com/serviceaccount/token
      - /var/run/docker.sock:/var/run/docker.sock
      # - /Users/<USER>/.docker/config.json:/root/.docker/config.json
      - /tmp:/tmp
    environment:
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      # - TESTCONTAINERS_RYUK_CONTAINER_PRIVILEGED=true
      # - TESTCONTAINERS_RYUK_DISABLED=true
      # - DOCKER_HOST=tcp://host.docker.internal:2375
      - TESTCONTAINERS_HOST_OVERRIDE=host.docker.internal
    # depends_on:
    #   - postgres
    #   - redis

  annout:
    image: local-dev-annout:latest
    container_name: annout
    ports:
      - 8030:8030
      - 8031:8031
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
    volumes:
      - ./projects/annout/config-${ENVIRONMENT:-dev}.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  annostat:
    image: local-dev-annostat:latest
    container_name: annostat
    ports:
      - 8040:8040
      - 8041:8041
    volumes:
      - ./projects/annostat/config-${ENVIRONMENT:-dev}.yaml:/data/conf/config.yaml
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  anycorn:
    image: local-dev-anycorn:latest
    container_name: anycorn
    ports:
      - 8050:8050
      - 8051:8051
    volumes:
      - ./projects/anycorn/config-${ENVIRONMENT:-dev}.yaml:/data/conf/anyconn.yaml
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
    networks:
      - dev-network
    # depends_on:
    #   - postgres
    #   - redis

  tars:
    image: local-dev-tars:latest
    container_name: tars
    ports:
      - 8070:8070
      - 8071:8071
    volumes:
      - ./projects/tars/config-${ENVIRONMENT:-dev}.yaml:/data/conf/tars.yaml
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-dev}
    networks:
      - dev-network

  nginx:
    image: nginx:latest
    container_name: nginx
    privileged: true
    restart: always
    ports:
      - 80:80
      - 443:443
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - ./nginx/logs:/var/log/nginx/
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/www/:/usr/share/nginx/html/
      - ./nginx/ssl:/etc/nginx/ssl/
    command: /bin/bash -c "nginx -g 'daemon off;'"
    networks:
      - dev-network
    depends_on:
      - iam
      - keto
      - anno
      - annofeed
      - annout
      - annostat

networks:
  dev-network:
    external: true
    name: local-dev_dev-network
