#!/bin/bash

# 方案1：环境变量配置方案启动脚本
# 使用方法：
# ./start.sh                    # 使用默认开发环境(.env)
# ./start.sh prod               # 使用生产环境(.env.prod)
# ./start.sh test               # 使用测试环境(.env.test)

set -e

# 默认环境为dev
ENVIRONMENT=${1:-dev}

# 环境配置文件映射
case $ENVIRONMENT in
    "dev")
        ENV_FILE=".env"
        echo "🚀 启动开发环境..."
        ;;
    "prod")
        ENV_FILE=".env.prod"
        echo "🚀 启动生产环境..."
        ;;
    "test")
        ENV_FILE=".env.test"
        echo "🚀 启动测试环境..."
        ;;
    *)
        echo "❌ 不支持的环境: $ENVIRONMENT"
        echo "支持的环境: dev, prod, test"
        exit 1
        ;;
esac

# 检查环境配置文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境配置文件不存在: $ENV_FILE"
    echo "请先创建对应的环境配置文件"
    exit 1
fi

echo "📋 使用配置文件: $ENV_FILE"

# 加载环境变量
export $(grep -v '^#' $ENV_FILE | xargs)

# 检查原有网络是否存在
NETWORK_NAME="local-dev_dev-network"
if ! docker network ls | grep -q "$NETWORK_NAME"; then
    echo "⚠️  原有网络 $NETWORK_NAME 不存在"
    echo "💡 请先在主目录启动基础服务："
    echo "   cd .. && docker-compose -f depends.yml up -d"
    echo "   或者运行: docker network create $NETWORK_NAME"
    echo ""
    read -p "是否继续启动？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 启动已取消"
        exit 1
    fi
fi

# 显示当前配置信息
echo "📊 当前配置信息:"
echo "   数据库主机: ${POSTGRES_HOST}"
echo "   数据库端口: ${POSTGRES_PORT}"
echo "   Redis地址: ${REDIS_ADDR}"
echo "   环境标识: ${ENVIRONMENT}"
echo "   网络: $NETWORK_NAME"

# 启动服务
echo "🔄 启动Docker Compose服务..."
docker-compose --env-file $ENV_FILE up -d

echo "✅ 服务启动完成！"
echo "📝 查看服务状态: docker-compose ps"
echo "📝 查看日志: docker-compose logs -f [service_name]"
echo ""
# echo "💡 优势："
# echo "   - 统一管理所有数据库配置"
# echo "   - 支持DSN格式连接字符串"
# echo "   - 一个文件修改，所有服务生效"
# echo "   - 支持多环境切换"

# test keto
# netstat -tlnp
# curl -f -6 http://localhost:4466/health/ready