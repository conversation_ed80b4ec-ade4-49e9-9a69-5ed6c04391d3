# version: '3'
services:

  keto:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/keto/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/keto/Dockerfile
    container_name: keto
    ports:
      - 4466:4466
      - 4467:4467
    volumes:
      - ./projects/keto/keto.yml:/home/<USER>/keto.yml
    environment:
      - "LOG_LEVEL=debug"
      - "DUMP_CACHE=false"
      - "SHOW_DB_QUERY=true"
      - "SHOW_OBJ_NAME=true"
      - "SQA_OPT_OUT=true"
      - "NAMESPACES_EXPERIMENTAL_STRICT_MODE=true"
      - "DBG_CACHE_REFRESH_SECONDS=600"
      - dsn=${KETO_DATABASE_DSN:-**********************************/keto_v2?sslmode=disable&max_conns=20&max_idle_conns=4&max_conn_lifetime=10h&max_conn_idle_time=1h}
    networks:
      - dev-network
    
    # 添加健康检查
    # 线上数据库 数据量大 初始化特别慢 需要延长时间
    healthcheck:
      test: ["CMD-SHELL", "curl -f -6 http://localhost:4466/health/ready || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 60
      start_period: 10s # 容器启动后，等待 10 秒再开始检查


  iam:
    build:
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/iam/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/iam/Dockerfile
    container_name: iam
    ports:
      - 8000:8000
      - 8001:8001
    volumes:
      - ./projects/iam/config.yaml:/data/conf/config.yaml
    environment:
      # Kratos框架环境变量配置（遵循data.database.xxx格式）
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${IAM_DB_NAME:-iam}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}
      # Redis配置
      # - DATA_REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      # - DATA_REDIS_READ_TIMEOUT=${DATA_REDIS_READ_TIMEOUT:-0.2s}
      # - DATA_REDIS_WRITE_TIMEOUT=${DATA_REDIS_WRITE_TIMEOUT:-0.2s}
      # 其他配置
      # - TEMPORAL_ADDR=${TEMPORAL_ADDR:-temporal:7233}
      # - KETO_READ_ADDR=${KETO_READ_ADDR:-keto:4466}
      # - KETO_WRITE_ADDR=${KETO_WRITE_ADDR:-keto:4467}
      # - ENVIRONMENT=${ENVIRONMENT:-dev}
    networks:
      - dev-network
    depends_on:
      keto:
        condition: service_healthy

  anno:
    build:
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/anno/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/anno/Dockerfile
      # dockerfile: Dockerfile.local
    container_name: anno
    ports:
      - 8010:8010
      - 8011:8011
    volumes:
      - ./projects/anno/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    environment:
      # Kratos框架环境变量配置（遵循data.database.xxx格式）
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${ANNO_DB_NAME:-anno}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}
      # Redis配置
      # - DATA_REDIS_ADDR=${REDIS_ADDR:-redis:6379}
      # - DATA_REDIS_READ_TIMEOUT=${DATA_REDIS_READ_TIMEOUT:-0.2s}
      # - DATA_REDIS_WRITE_TIMEOUT=${DATA_REDIS_WRITE_TIMEOUT:-0.2s}
      # 其他配置
      # - TEMPORAL_ADDR=${TEMPORAL_ADDR:-temporal:7233}
      # - ENVIRONMENT=${ENVIRONMENT:-dev}
      # AWS配置
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      - "LOT_ENABLE_JOB_CAM_PARAMS=true"
    depends_on:
      - iam
      - annofeed
      - annout
      - annostat

  annofeed:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annofeed/
      dockerfile: /Users/<USER>/docker/local-dev//env-config-solution/projects/annofeed/Dockerfile
    container_name: annofeed
    ports:
      - 8020:8020
      - 8021:8021
    networks:
      - dev-network
    volumes:
      - ./projects/annofeed/config.yaml:/data/conf/config.yaml
      - ./projects/annofeed/token:/var/run/secrets/eks.amazonaws.com/serviceaccount/token
      - /var/run/docker.sock:/var/run/docker.sock
      # - /Users/<USER>/.docker/config.json:/root/.docker/config.json
      - /tmp:/tmp
    environment:
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${ANNOFEED_DB_NAME:-annofeed}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}
      # 业务配置
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
      # docker mac
      # - TESTCONTAINERS_RYUK_CONTAINER_PRIVILEGED=true
      # - TESTCONTAINERS_RYUK_DISABLED=true
      # - DOCKER_HOST=tcp://host.docker.internal:2375
      - TESTCONTAINERS_HOST_OVERRIDE=host.docker.internal

  annout:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annout/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/annout/Dockerfile
    container_name: annout
    ports:
      - 8030:8030
      - 8031:8031
    environment:
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${ANNOUT_DB_NAME:-annout}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}
      # AWS配置
      # - "DBG_SAVE_BIG_VALUES_IN_DB=true"
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      # - "AWS_ROLE_ARN=arn:aws-cn:iam::************:role/non-prod-workload-sansheng"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
      # - "AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
    volumes:
      - ./projects/annout/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network

  annostat:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/annostat/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/annostat/Dockerfile
    container_name: annostat
    ports:
      - 8040:8040
      - 8041:8041
    volumes:
      - ./projects/annostat/config.yaml:/data/conf/config.yaml
    networks:
      - dev-network
    environment:
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${ANNOSTAT_DB_NAME:-annostat}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}

  anycorn:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/unicorn/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/anycorn/Dockerfile
    container_name: anycorn
    ports:
      - 8050:8050
      - 8051:8051
    volumes:
      - ./projects/anycorn/config.yaml:/data/conf/anyconn.yaml
    environment:
      - data.database.driver=postgres
      - data.database.endpoint=${POSTGRES_HOST:-postgres}
      - data.database.port=${POSTGRES_PORT:-5432}
      - data.database.database=${ANYCORN_DB_NAME:-anyconn}
      - data.database.username=${POSTGRES_USER:-root}
      - data.database.password=${POSTGRES_PASSWORD:-root}
      - data.database.options=${POSTGRES_OPTIONS:-sslmode=disable}
      - data.database.max_idle_conns=${DATA_DATABASE_MAX_IDLE_CONNS:-5}
      - data.database.max_open_conns=${DATA_DATABASE_MAX_OPEN_CONNS:-20}
      - data.database.conn_max_idle_time=${DATA_DATABASE_CONN_MAX_IDLE_TIME:-600s}
      # AWS配置
      - "AWS_REGION=cn-northwest-1"
      - "AWS_DEFAULT_REGION=cn-northwest-1"
      - "AWS_ACCESS_KEY_ID=********************"
      - "AWS_SECRET_ACCESS_KEY=0GkNLOrRUfb5fTqK5VGQ/QS+B+XdhE89Ut25E/f2"
      - "AWS_STS_REGIONAL_ENDPOINTS=regional"
    networks:
      - dev-network

  tars:
    build: 
      context: /Users/<USER>/goproject/src/gitlab.rp.konvery.work/platform/konvery-dev/unicorn/
      dockerfile: /Users/<USER>/docker/local-dev/env-config-solution/projects/tars/Dockerfile
    container_name: tars
    ports:
      - 8070:8070
      - 8071:8071
    volumes:
      - ./projects/tars/config.yaml:/data/conf/tars.yaml
    networks:
      - dev-network

  nginx:
    image: nginx:latest
    container_name: nginx
    privileged: true
    restart: always
    ports:
      - 80:80
      - 443:443
    environment:
      - "TZ=Asia/Shanghai"
    volumes:
      - ./nginx/logs:/var/log/nginx/
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/www/:/usr/share/nginx/html/
      - ./nginx/ssl:/etc/nginx/ssl/
    command: /bin/bash -c "nginx -g 'daemon off;'"
    networks:
      - dev-network
    depends_on:
      - iam
      - keto
      - anno
      - annofeed
      - annout
      - annostat

networks:
  dev-network:
    external: true
    name: local-dev_dev-network
