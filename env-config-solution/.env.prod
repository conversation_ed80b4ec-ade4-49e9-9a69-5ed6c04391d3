# 生产环境配置
# 数据库配置
POSTGRES_HOST=host.docker.internal
POSTGRES_PORT=5433
POSTGRES_USER=sansheng
POSTGRES_PASSWORD=hNHKO2w9Q2JDJ4U8UrT8iwkjVMd
POSTGRES_OPTIONS=sslmode=disable

# Redis配置
# REDIS_HOST=prod-redis.example.com
# REDIS_PORT=6379
# REDIS_PASSWORD=prod_redis_password

# 各服务数据库名
IAM_DB_NAME=sansheng-usercenter
ANNO_DB_NAME=sansheng-core
ANNOFEED_DB_NAME=sansheng-source
ANNOUT_DB_NAME=sansheng-export
ANNOSTAT_DB_NAME=sansheng-annostat
ANYCORN_DB_NAME=anyconn
# TARS_DB_NAME=tars_prod

# DSN格式连接字符串（推荐使用）
# IAM_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/iam_prod?sslmode=require
# ANNO_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/anno_prod?sslmode=require
# ANNOFEED_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annofeed_prod?sslmode=require
# ANNOUT_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annout_prod?sslmode=require
# ANNOSTAT_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/annostat_prod?sslmode=require
# ANYCORN_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/anycorn_prod?sslmode=require
# TARS_DATABASE_DSN=postgres://prod_user:<EMAIL>:5432/tars_prod?sslmode=require
KETO_DATABASE_DSN=postgres://keto:<EMAIL>:5433/keto?sslmode=disable&max_conns=20&max_idle_conns=4&max_conn_lifetime=10h&max_conn_idle_time=1h

# Redis连接字符串
# REDIS_ADDR=prod-redis.example.com:6379

# Kratos框架数据库连接配置
DATA_DATABASE_MAX_IDLE_CONNS=5
DATA_DATABASE_MAX_OPEN_CONNS=20
DATA_DATABASE_CONN_MAX_IDLE_TIME=600s

# Redis超时配置
# DATA_REDIS_READ_TIMEOUT=0.2s
# DATA_REDIS_WRITE_TIMEOUT=0.2s

# 其他公共配置
# TEMPORAL_ADDR=prod-temporal.example.com:7233
# KETO_READ_ADDR=prod-keto.example.com:4466
# KETO_WRITE_ADDR=prod-keto.example.com:4467

# 环境标识
ENVIRONMENT=prod

# MinIO配置（生产环境）
# minio_ak=PROD_MINIO_ACCESS_KEY
# minio_sk=PROD_MINIO_SECRET_KEY
# minio_endpoint=https://prod-minio.example.com
# minio-bucket=prod-minio-bucket

# anno config
ANNO_MQ_PRODUCER_TOPIC=arn:aws-cn:sns:cn-northwest-1:035532479701:prod-workload-sns-sansheng-anno
ANNO_MQ_SQS_NAME=prod-workload-sqs-sansheng-anno
ANNO_OBJECT_STORAGE_S3_ACCESS_KEY=********************
ANNO_OBJECT_STORAGE_S3_SECRET_KEY=WRAHGVY/XofV/5tFE+lzj+rcuPfqISZjQHv0egap
ANNO_OBJECT_STORAGE_S3_BUCKET=prod-workload-sansheng
ANNO_OBJECT_STORAGE_S3_PUBLIC_BUCKET=konvery-images-public

# annofeed config
ANNOFEED_MQ_PRODUCER_PROVIDER=redis_pubsub
ANNOFEED_MQ_PRODUCER_TOPIC=topic: arn:aws-cn:sns:cn-northwest-1:035532479701:prod-workload-sns-sansheng-annofeed
ANNOFEED_MQ_CONSUMER_TOPICS=arn:aws-cn:sns:cn-northwest-1:035532479701:prod-workload-sns-sansheng-annofeed
ANNOFEED_MQ_SQS_NAME=prod-workload-sqs-sansheng-annofeed
ANNOFEED_S3_ACCESS_KEY=********************
ANNOFEED_S3_SECRET_KEY=WRAHGVY/XofV/5tFE+lzj+rcuPfqISZjQHv0egap
ANNOFEED_FILE_SERVER_BUCKET=prod-workload-sansheng
ANNOFEED_FILE_SERVER_PUBLIC_BUCKET=konvery-images-public
ANNOFEED_FILE_SERVER_PRESIGN_EXPIRES=259200s

# annostat config
ANNOSTAT_OBJECT_STORAGE_S3_ACCESS_KEY=********************
ANNOSTAT_OBJECT_STORAGE_S3_SECRET_KEY=WRAHGVY/XofV/5tFE+lzj+rcuPfqISZjQHv0egap
ANNOSTAT_OBJECT_STORAGE_S3_BUCKET=prod-workload-sansheng
ANNOSTAT_OBJECT_STORAGE_S3_PUBLIC_BUCKET=konvery-images-public

# annostat config
ANNOUT_OBJECT_STORAGE_S3_ACCESS_KEY=********************
ANNOUT_OBJECT_STORAGE_S3_SECRET_KEY=WRAHGVY/XofV/5tFE+lzj+rcuPfqISZjQHv0egap
ANNOUT_OBJECT_STORAGE_S3_BUCKET=prod-workload-sansheng
ANNOUT_OBJECT_STORAGE_S3_PUBLIC_BUCKET=konvery-images-public